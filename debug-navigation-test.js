// Debug script to test navigation functionality
console.log('=== NAVIGATION DEBUG TEST ===');

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, testing navigation...');
    
    // Test 1: Check if sidebar items exist
    const sidebarItems = document.querySelectorAll('.sidebar-item');
    console.log('Found sidebar items:', sidebarItems.length);
    sidebarItems.forEach((item, index) => {
        console.log(`Sidebar item ${index}:`, item.textContent.trim(), 'data-section:', item.dataset.section);
    });
    
    // Test 2: Check if content sections exist
    const contentSections = document.querySelectorAll('.content-section');
    console.log('Found content sections:', contentSections.length);
    contentSections.forEach((section, index) => {
        console.log(`Content section ${index}:`, section.id, 'active:', section.classList.contains('active'));
    });
    
    // Test 3: Check if switchToSection function exists
    if (typeof switchToSection === 'function') {
        console.log('✅ switchToSection function exists');
    } else {
        console.log('❌ switchToSection function NOT found');
    }
    
    // Test 4: Manually test clicking explore
    setTimeout(() => {
        console.log('\n--- Testing manual click on Explore ---');
        const exploreItem = document.querySelector('.sidebar-item[data-section="explore"]');
        if (exploreItem) {
            console.log('Found explore item, simulating click...');
            exploreItem.click();
            
            setTimeout(() => {
                const exploreSection = document.getElementById('explore-section');
                const isActive = exploreSection && exploreSection.classList.contains('active');
                console.log('Explore section active after click:', isActive);
                
                if (isActive) {
                    console.log('✅ Navigation working!');
                } else {
                    console.log('❌ Navigation failed!');
                    console.log('Explore section:', exploreSection);
                    console.log('Classes:', exploreSection ? exploreSection.className : 'section not found');
                }
            }, 100);
        } else {
            console.log('❌ Explore item not found');
        }
    }, 1000);
    
    // Test 5: Check for any JavaScript errors
    window.addEventListener('error', function(e) {
        console.error('JavaScript Error:', e.error);
    });
});

// Test 6: Check if navigation is initialized
setTimeout(() => {
    console.log('\n--- Checking navigation initialization ---');
    const sidebarItems = document.querySelectorAll('.sidebar-item');
    let hasEventListeners = false;
    
    sidebarItems.forEach(item => {
        // Try to trigger a click and see if it logs anything
        const originalLog = console.log;
        let clickDetected = false;
        
        console.log = function(...args) {
            if (args[0] && args[0].includes && args[0].includes('Sidebar item clicked')) {
                clickDetected = true;
            }
            originalLog.apply(console, args);
        };
        
        item.click();
        
        if (clickDetected) {
            hasEventListeners = true;
        }
        
        console.log = originalLog;
    });
    
    if (hasEventListeners) {
        console.log('✅ Event listeners are attached');
    } else {
        console.log('❌ Event listeners may not be attached');
    }
}, 2000);
